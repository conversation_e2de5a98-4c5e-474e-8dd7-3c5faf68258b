<template>
  <!-- #ifdef MP-WEIXIN -->
  <uv-navbar :fixed="false" title="活动详情" left-arrow @leftClick="$onClickLeft" />
  <!-- #endif -->
  <view class="activity-detail">
    <!-- 活动头图 -->
    <view class="activity-header">
      <image :src="activityInfo.image" mode="aspectFill" class="header-image"></image>
      <view class="header-overlay">
        <view class="activity-title">{{ activityInfo.name }}</view>
        <view class="activity-subtitle">{{ activityInfo.content }}</view>
      </view>
    </view>

    <!-- 我的进度卡片 -->
    <view class="progress-card" v-if="userProgress">
      <view class="card-header">
        <text class="card-title">我的进度</text>
        <view class="status-badge" :class="getStatusClass(userProgress.status)">
          {{ getStatusText(userProgress.status) }}
        </view>
      </view>

      <view class="progress-content">
        <view class="progress-circle">
          <view class="circle-progress" :style="getCircleStyle()">
            <text class="progress-text">{{ userProgress.currentNumber }}/{{ userProgress.targetNumber }}</text>
          </view>
        </view>

        <view class="progress-info">
          <view class="info-item">
            <text class="info-label">完成进度</text>
            <text class="info-value">{{ userProgress.progressPercentage }}%</text>
          </view>
          <view class="info-item">
            <text class="info-label">还需邀请</text>
            <text class="info-value">{{ Math.max(0, userProgress.targetNumber - userProgress.currentNumber) }}人</text>
          </view>
        </view>
      </view>

      <view class="action-buttons">
        <button class="btn btn-primary" @click="shareActivity" v-if="userProgress.status === 0">
          邀请好友
        </button>
        <button class="btn btn-success" @click="claimReward"
          v-if="userProgress.canClaimReward && !userProgress.hasClaimedReward">
          领取奖励
        </button>
        <button class="btn btn-completed" disabled v-if="userProgress.hasClaimedReward">
          已领取奖励
        </button>
      </view>
    </view>

    <!-- 参与按钮 -->
    <view class="join-section" v-if="!userProgress">
      <button class="btn btn-join" @click="joinActivity">
        立即参与活动
      </button>
    </view>

    <!-- 活动奖励 -->
    <view class="reward-section">
      <view class="section-title">活动奖励</view>
      <view class="reward-list">
        <view class="reward-item" v-for="reward in activityInfo.products" :key="reward.id">
          <image :src="reward.image" class="reward-icon"></image>
          <view class="reward-info">
            <text class="reward-name">{{ reward.name }}</text>
            <text class="reward-desc">{{ getRewardDesc(reward) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请记录 -->
    <view class="invite-section" v-if="userProgress && userProgress.invitees.length > 0">
      <view class="section-title">我的邀请</view>
      <view class="invite-list">
        <view class="invite-item" v-for="invitee in userProgress.invitees" :key="invitee.id">
          <image :src="invitee.inviteeAvatar || '/static/images/default-avatar.png'" class="invite-avatar"></image>
          <view class="invite-info">
            <text class="invite-name">{{ invitee.inviteeNickname }}</text>
            <text class="invite-time">{{ formatTime(invitee.inviteTime) }}</text>
          </view>
          <view class="invite-status" :class="{ success: invitee.status === 1 }">
            {{ invitee.status === 1 ? '有效' : '无效' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 活动规则 -->
    <view class="rules-section">
      <view class="section-title">
        <uni-icons type="list" size="16" color="#007aff"></uni-icons>
        <text>活动规则</text>
        <view class="expand-btn" @click="toggleRules">
          <uni-icons :type="rulesExpanded ? 'up' : 'down'" size="14" color="#007aff"></uni-icons>
        </view>
      </view>

      <view class="rules-content" :class="{ expanded: rulesExpanded }">
        <view class="rule-category">
          <text class="category-title">参与规则</text>
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">用户需要先注册并登录小程序</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">点击"立即参与"按钮加入活动</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">每个用户只能参与一次同一活动</text>
          </view>
        </view>

        <view class="rule-category">
          <text class="category-title">邀请规则</text>
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">参与活动后获得专属邀请码和分享链接</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">通过微信分享、海报分享等方式邀请好友</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">好友通过您的邀请链接注册并参与活动算作有效邀请</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">4</text>
            <text class="rule-text">邀请的好友必须是新用户（未注册过的用户）</text>
          </view>
        </view>

        <view class="rule-category">
          <text class="category-title">奖励规则</text>
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">达到邀请目标人数即可领取对应奖励</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">奖励包括积分、优惠券等，具体以活动页面显示为准</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">奖励需要在活动结束后30天内领取，逾期作废</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">4</text>
            <text class="rule-text">奖励发放到用户账户，可在"我的奖励"中查看</text>
          </view>
        </view>

        <view class="rule-category">
          <text class="category-title">注意事项</text>
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">活动期间如发现作弊行为，将取消参与资格</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">奖励数量有限，先到先得</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">活动最终解释权归平台所有</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 活动时间 -->
    <view class="time-section">
      <view class="section-title">
        <uni-icons type="calendar" size="16" color="#007aff"></uni-icons>
        <text>活动时间</text>
      </view>
      <view class="time-content">
        <view class="time-item">
          <text class="time-label">开始时间：</text>
          <text class="time-value">{{ formatDate(activityInfo.startTime) }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">结束时间：</text>
          <text class="time-value">{{ formatDate(activityInfo.endTime) }}</text>
        </view>
        <view class="time-item" v-if="getTimeRemaining()">
          <text class="time-label">剩余时间：</text>
          <text class="time-value countdown">{{ getTimeRemaining() }}</text>
        </view>
      </view>
    </view>

    <!-- 排行榜 -->
    <view class="ranking-section">
      <view class="section-title">邀请排行榜</view>
      <view class="ranking-list">
        <view class="ranking-item" v-for="(item, index) in rankingList" :key="item.userId">
          <view class="ranking-number" :class="getRankingClass(index)">
            {{ index + 1 }}
          </view>
          <image :src="item.avatar || '/static/images/default-avatar.png'" class="ranking-avatar"></image>
          <view class="ranking-info">
            <text class="ranking-name">{{ item.nickname }}</text>
            <text class="ranking-count">邀请了{{ item.inviteCount }}人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享弹窗 -->
    <share-modal :visible="shareModalVisible" :share-info="shareInfo" @close="closeShareModal"
      @share-success="handleShareSuccess" @share-fail="handleShareFail" @generate-poster="handleGeneratePoster"
      @copy-link="handleCopyLink" @copy-invite-code="handleCopyInviteCode"></share-modal>

    <!-- 奖励领取弹窗 -->
    <uv-modal ref="rewardModal" title="恭喜获得奖励！" :show-cancel-button="false" confirm-text="确定" confirm-color="#007aff"
      :close-on-click-overlay="false" width="600rpx" @confirm="closeRewardPopup">
      <view class="reward-popup-content">
        <view class="reward-success-icon">
          <image src="/static/images/reward-success.png" class="success-icon"></image>
        </view>
        <view class="reward-details">
          <view class="reward-detail" v-for="reward in claimedRewards" :key="reward.type">
            <image :src="reward.image" class="detail-icon"></image>
            <text class="detail-text">{{ reward.name }} x{{ reward.amount }}</text>
          </view>
        </view>
      </view>
    </uv-modal>
  </view>
</template>

<script>
import {
  getActivityDetail,
  getUserProgress,
  joinActivity,
  claimReward,
  getActivityRanking,
  shareActivity
} from '@/api/activity'
import {
  userGetUserInfo,
} from '@/api/user'
import { handleSceneParams, handleHotStartSceneParams } from '@/utils/scene-parser'
import ShareModal from '@/components/share-modal/share-modal.vue'
import wechatShare from '@/utils/wechat-share.js'

export default {
  components: {
    ShareModal
  },

  data() {
    return {
      userInfo: {},
      activityId: null,
      inviterUserId: null,
      activityInfo: {},
      userProgress: null,
      rankingList: [],
      claimedRewards: [],
      loading: false,
      shareModalVisible: false,
      shareInfo: {
        shareTitle: '',
        shareDesc: '',
        shareImage: '',
        miniProgramPath: ''
      },
      rulesExpanded: false
    }
  },

  onLoad(options) {
    console.log('onLoad options:', options)

    // 使用工具函数处理scene参数
    const params = handleSceneParams(options)

    this.activityId = params.activityId
    this.inviterUserId = params.inviterUserId

    console.log('最终参数 - activityId:', this.activityId, 'inviterUserId:', this.inviterUserId, 'source:', params.source)

    if (this.activityId) {
      this.loadActivityDetail()
      this.loadUserProgress()
      this.loadRanking()
    } else {
      uni.showToast({
        title: '活动ID不能为空',
        icon: 'error'
      })
    }
  },

  onShow() {
    // 处理小程序热启动时的scene参数
    if (!this.activityId) {
      const params = handleHotStartSceneParams()
      if (params.activityId) {
        this.activityId = params.activityId
        this.inviterUserId = params.inviterUserId

        console.log('热启动获取参数 - activityId:', this.activityId, 'inviterUserId:', this.inviterUserId)

        // 重新加载数据
        this.loadActivityDetail()
        this.loadUserProgress()
        this.loadRanking()
      }
    }

    // 页面显示时刷新进度
    if (this.activityId) {
      this.loadUserProgress()
    }
    this.getUserInfo()
  },

  // 小程序分享配置
  onShareAppMessage() {
    return {
      title: this.shareInfo.shareTitle || this.activityInfo.name || '精彩活动邀请您参与',
      path: `/pages/components/pages/activity/detail?id=${this.activityId}&inviter=${this.userInfo.id}`,
      imageUrl: this.shareInfo.shareImage || this.activityInfo.image
    }
  },

  methods: {
    async getUserInfo() {
        let data = await userGetUserInfo();
        if (data) {
          this.userInfo = data;
        }
    },
    // 加载活动详情
    async loadActivityDetail() {
      try {
        const res = await getActivityDetail(this.activityId)
        this.activityInfo = res
      } catch (error) {
        console.error('加载活动详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 加载用户进度
    async loadUserProgress() {
      try {
        const res = await getUserProgress(this.activityId)
        this.userProgress = res
      } catch (error) {
        console.error('加载用户进度失败:', error)
      }
    },

    // 加载排行榜
    async loadRanking() {
      try {
        const res = await getActivityRanking(this.activityId, 10)
        this.rankingList = res.rankingList || []
      } catch (error) {
        console.error('加载排行榜失败:', error)
      }
    },

    // 参与活动
    async joinActivity() {
      try {
        uni.showLoading({ title: '参与中...' })

        const inviterUserId = this.getInviterFromUrl()
        await joinActivity({
          activityId: this.activityId,
          inviterUserId
        })

        uni.hideLoading()
        uni.showToast({
          title: '参与成功',
          icon: 'success'
        })

        // 刷新进度
        this.loadUserProgress()

      } catch (error) {
        uni.hideLoading()
        console.error('参与活动失败:', error)
        uni.showToast({
          title: error.message || '参与失败',
          icon: 'none'
        })
      }
    },

    // 分享活动
    async shareActivity() {
      try {
        // 获取分享信息
        const res = await shareActivity({
          activityId: this.activityId,
          shareType: 1
        })

        this.shareInfo = res
        this.shareModalVisible = true
      } catch (error) {
        console.error('获取分享信息失败:', error)
        uni.showToast({
          title: '获取分享信息失败',
          icon: 'none'
        })
      }
    },

    // 关闭分享弹窗
    closeShareModal() {
      this.shareModalVisible = false
    },

    // 分享成功回调
    handleShareSuccess(data) {
      console.log('分享成功:', data)
      // 记录分享行为
      wechatShare.recordShareAction({
        activityId: this.activityId,
        shareType: data.type,
        platform: data.platform
      })
    },

    // 分享失败回调
    handleShareFail(data) {
      console.error('分享失败:', data)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    },

    // 生成海报
    handleGeneratePoster() {
      uni.navigateTo({
        url: `/pages/components/pages/activity/poster?activityId=${this.activityId}`
      })
    },

    // 复制链接
    handleCopyLink(url) {
      console.log('链接已复制:', url)
    },

    // 领取奖励
    async claimReward() {
      try {
        uni.showLoading({ title: '领取中...' })

        const res = await claimReward({
          activityId: this.activityId
        })

        uni.hideLoading()

        if (res.success) {
          this.claimedRewards = res.rewards
          this.$refs.rewardModal.open()
          this.loadUserProgress() // 刷新进度
        } else {
          uni.showToast({
            title: res.message || '领取失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.hideLoading()
        console.error('领取奖励失败:', error)
        uni.showToast({
          title: '领取失败',
          icon: 'none'
        })
      }
    },

    // 关闭奖励弹窗
    closeRewardPopup() {
      this.$refs.rewardModal.close()
    },

    // 获取邀请人ID
    getInviterFromUrl() {
      // 从页面参数中获取邀请人ID
      return this.inviterUserId || null
    },

    // 获取状态样式
    getStatusClass(status) {
      const classMap = {
        0: 'ongoing',
        1: 'completed',
        2: 'claimed'
      }
      return classMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '进行中',
        1: '已完成',
        2: '已领取'
      }
      return textMap[status] || '未知'
    },

    // 获取圆形进度样式
    getCircleStyle() {
      if (!this.userProgress) return {}

      const percentage = this.userProgress.progressPercentage || 0
      const degree = (percentage / 100) * 360

      return {
        background: `conic-gradient(#007aff 0deg, #007aff ${degree}deg, #f0f0f0 ${degree}deg, #f0f0f0 360deg)`
      }
    },

    // 获取奖励描述
    getRewardDesc(reward) {
      if (reward.type === 0) {
        return `${reward.number}积分`
      } else {
        return `${reward.number}张优惠券`
      }
    },

    // 获取排名样式
    getRankingClass(index) {
      if (index === 0) return 'first'
      if (index === 1) return 'second'
      if (index === 2) return 'third'
      return 'normal'
    },

    // 格式化时间
    formatTime(time) {
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      return Math.floor(diff / 86400000) + '天前'
    },

    // 格式化日期
    formatDate(time) {
      if (!time) return '--'
      const date = new Date(time)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    // 获取剩余时间
    getTimeRemaining() {
      if (!this.activityInfo.endTime) return ''

      const now = new Date()
      const endTime = new Date(this.activityInfo.endTime)
      const diff = endTime - now

      if (diff <= 0) return '活动已结束'

      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

      if (days > 0) return `${days}天${hours}小时`
      if (hours > 0) return `${hours}小时${minutes}分钟`
      return `${minutes}分钟`
    },

    // 切换规则展开状态
    toggleRules() {
      this.rulesExpanded = !this.rulesExpanded
    },

    // 获取当前用户ID
    getCurrentUserId() {
      // 这里应该从用户状态管理或本地存储中获取用户ID
      // 示例：从uni.getStorageSync获取
      return uni.getStorageSync('userId') || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.activity-header {
  position: relative;
  height: 400rpx;

  .header-image {
    width: 100%;
    height: 100%;
  }

  .header-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 60rpx 30rpx 30rpx;
    color: white;

    .activity-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }

    .activity-subtitle {
      font-size: 26rpx;
      opacity: 0.9;
    }
  }
}

.progress-card {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .status-badge {
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: white;

      &.ongoing {
        background: #007aff;
      }

      &.completed {
        background: #34c759;
      }

      &.claimed {
        background: #ff9500;
      }
    }
  }

  .progress-content {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .progress-circle {
      width: 120rpx;
      height: 120rpx;
      margin-right: 40rpx;

      .circle-progress {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .progress-text {
          font-size: 24rpx;
          font-weight: bold;
          color: #007aff;
        }
      }
    }

    .progress-info {
      flex: 1;

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .info-label {
          font-size: 28rpx;
          color: #666;
        }

        .info-value {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;

    .btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: bold;
      border: none;

      &.btn-primary {
        background: linear-gradient(90deg, #007aff, #00d4ff);
        color: white;
      }

      &.btn-success {
        background: linear-gradient(90deg, #ff6b35, #ff8f00);
        color: white;
      }

      &.btn-completed {
        background: #f0f0f0;
        color: #999;
      }
    }
  }
}

.join-section {
  padding: 20rpx;

  .btn-join {
    width: 100%;
    height: 100rpx;
    background: linear-gradient(90deg, #007aff, #00d4ff);
    color: white;
    border-radius: 50rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
  }
}

.reward-section,
.invite-section,
.rules-section,
.ranking-section,
.time-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;

    text {
      margin-left: 10rpx;
    }

    .expand-btn {
      padding: 10rpx;
    }
  }
}

.reward-list {
  .reward-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .reward-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }

    .reward-info {
      flex: 1;

      .reward-name {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 5rpx;
      }

      .reward-desc {
        font-size: 24rpx;
        color: #ff6b35;
      }
    }
  }
}

.invite-list {
  .invite-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .invite-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .invite-info {
      flex: 1;

      .invite-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 5rpx;
      }

      .invite-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .invite-status {
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      background: #f0f0f0;
      color: #999;

      &.success {
        background: #e8f5e8;
        color: #34c759;
      }
    }
  }
}

.rules-content {
  max-height: 400rpx;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: none;
  }

  .rule-category {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .category-title {
      display: block;
      font-size: 28rpx;
      font-weight: bold;
      color: #007aff;
      margin-bottom: 20rpx;
      padding-bottom: 10rpx;
      border-bottom: 2rpx solid #f0f8ff;
    }
  }

  .rule-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15rpx;

    .rule-number {
      width: 32rpx;
      height: 32rpx;
      background: #007aff;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      margin-right: 15rpx;
      flex-shrink: 0;
    }

    .rule-text {
      flex: 1;
      font-size: 26rpx;
      color: #666;
      line-height: 36rpx;
    }
  }
}

.time-content {
  .time-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .time-label {
      font-size: 28rpx;
      color: #666;
    }

    .time-value {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;

      &.countdown {
        color: #ff6b35;
      }
    }
  }
}

.ranking-list {
  .ranking-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .ranking-number {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: bold;
      margin-right: 20rpx;

      &.first {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #333;
      }

      &.second {
        background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
        color: #333;
      }

      &.third {
        background: linear-gradient(135deg, #cd7f32, #daa520);
        color: white;
      }

      &.normal {
        background: #f0f0f0;
        color: #666;
      }
    }

    .ranking-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .ranking-info {
      flex: 1;

      .ranking-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 5rpx;
      }

      .ranking-count {
        font-size: 24rpx;
        color: #007aff;
      }
    }
  }
}

.share-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;

  .popup-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 40rpx;
  }

  .share-options {
    display: flex;
    justify-content: space-around;
    margin-bottom: 40rpx;

    .share-option {
      display: flex;
      flex-direction: column;
      align-items: center;

      .share-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .cancel-btn {
    width: 100%;
    height: 80rpx;
    background: #f0f0f0;
    color: #666;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
  }
}

/* 奖励弹窗内容样式 */
.reward-popup-content {
  padding: 20rpx 0;
  text-align: center;
}

.reward-success-icon {
  text-align: center;
  margin-bottom: 30rpx;
}

.reward-success-icon .success-icon {
  width: 100rpx;
  height: 100rpx;
  display: block;
  margin: 0 auto;
}

.reward-details {
  margin-bottom: 20rpx;
}

.reward-detail {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  justify-content: flex-start;
}

.reward-detail:last-child {
  border-bottom: none;
}

.reward-detail .detail-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.reward-detail .detail-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: left;
}
</style>
