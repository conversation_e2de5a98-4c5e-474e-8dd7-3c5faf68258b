<template>
  <view class="container">
    <view class="header">
      <text class="title">登录流程测试</text>
    </view>
    
    <view class="section">
      <text class="section-title">当前状态</text>
      <view class="status-item">
        <text>登录状态: {{ isLogin ? '已登录' : '未登录' }}</text>
      </view>
      <view class="status-item" v-if="isLogin">
        <text>用户信息: {{ JSON.stringify(member, null, 2) }}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">页面状态</text>
      <view class="status-item">
        <text>Activity ID: {{ activityId || '未设置' }}</text>
      </view>
      <view class="status-item">
        <text>Inviter User ID: {{ inviterUserId || '未设置' }}</text>
      </view>
      <view class="status-item">
        <text>保存的页面状态: {{ savedPageState ? JSON.stringify(savedPageState, null, 2) : '无' }}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">测试操作</text>
      
      <button class="test-btn" @click="setTestParams">设置测试参数</button>
      <button class="test-btn" @click="testLoginFlow">测试登录流程</button>
      <button class="test-btn" @click="clearPageState">清除页面状态</button>
      <button class="test-btn" @click="logout">退出登录</button>
      <button class="test-btn" @click="goToActivityDetail">跳转到活动详情</button>
    </view>
    
    <view class="section">
      <text class="section-title">测试日志</text>
      <view class="log-container">
        <view class="log-item" v-for="(log, index) in logs" :key="index">
          <text class="log-text">{{ log }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMainStore } from '@/store/store'
import { storeToRefs } from 'pinia'
import { handleLoginFailure, getPageState, clearPageState as clearPageStateUtil, savePageState } from '@/utils/index'

const main = useMainStore()
const { member, isLogin } = storeToRefs(main)

const activityId = ref(null)
const inviterUserId = ref(null)
const savedPageState = ref(null)
const logs = ref([])

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

const refreshPageState = () => {
  savedPageState.value = getPageState()
  addLog('刷新页面状态: ' + (savedPageState.value ? '有保存状态' : '无保存状态'))
}

const setTestParams = () => {
  activityId.value = 2
  inviterUserId.value = 24
  addLog('设置测试参数: activityId=2, inviterUserId=24')
}

const testLoginFlow = () => {
  if (isLogin.value) {
    addLog('用户已登录，无需测试登录流程')
    return
  }
  
  const pageParams = {
    activityId: activityId.value,
    inviterUserId: inviterUserId.value
  }
  
  addLog('开始测试登录流程，保存页面参数: ' + JSON.stringify(pageParams))
  
  handleLoginFailure({
    returnUrl: '/pages/test/login-flow-test',
    pageParams: pageParams,
    source: 'test'
  })
}

const clearPageState = () => {
  clearPageStateUtil()
  refreshPageState()
  addLog('清除页面状态')
}

const logout = () => {
  uni.removeStorageSync('userinfo')
  uni.removeStorageSync('accessToken')
  main.SET_MEMBER({})
  main.SET_TOKEN('')
  addLog('退出登录')
}

const goToActivityDetail = () => {
  const url = `/pages/components/pages/activity/detail?id=${activityId.value || 2}&inviter=${inviterUserId.value || 24}`
  addLog('跳转到活动详情: ' + url)
  uni.navigateTo({
    url: url
  })
}

onMounted(() => {
  refreshPageState()
  addLog('页面加载完成')
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.status-item {
  margin-bottom: 10rpx;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 5rpx;
}

.test-btn {
  width: 100%;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.test-btn:active {
  background-color: #0056cc;
}

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  margin-bottom: 8rpx;
  padding: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  border-left: 3rpx solid #007aff;
}

.log-text {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
}
</style>
