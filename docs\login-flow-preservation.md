# 登录流程页面状态保存方案

## 概述

本方案解决了微信小程序中用户通过二维码扫描进入活动页面时，如果需要登录，登录成功后能够正确返回原页面并保持邀请人绑定关系的问题。

## 问题描述

用户扫描二维码进入活动详情页面，URL包含scene参数（如：`scene=activityId%253D2%2526inviter%253D24`），当用户需要登录时：

1. 原有流程：跳转到登录页 → 登录成功 → 简单返回上一页
2. 问题：返回时丢失了scene参数和邀请人信息

## 解决方案

### 1. 页面状态保存机制

在 `utils/index.js` 中新增了三个核心函数：

```javascript
// 保存页面状态
export const savePageState = (pageState) => {
  uni.setStorageSync('pageState', pageState)
}

// 获取页面状态
export const getPageState = () => {
  return uni.getStorageSync('pageState') || null
}

// 清除页面状态
export const clearPageState = () => {
  uni.removeStorageSync('pageState')
}
```

### 2. 增强的登录失败处理

更新了 `handleLoginFailure` 函数，支持保存当前页面状态：

```javascript
export const handleLoginFailure = (options = {}) => {
  // 清除用户信息
  uni.removeStorageSync('userinfo')
  uni.removeStorageSync('accessToken')
  const main = useMainStore()
  main.SET_MEMBER({})
  main.SET_TOKEN('')
  
  // 保存当前页面状态
  if (options.returnUrl || options.pageParams) {
    const pageState = {
      returnUrl: options.returnUrl,
      pageParams: options.pageParams,
      source: options.source || 'unknown',
      timestamp: Date.now()
    }
    savePageState(pageState)
  }
  
  // 跳转到登录页
  uni.redirectTo({
    url: '/pages/components/pages/login/login',
  })
}
```

### 3. 活动详情页面改进

#### 页面状态恢复

在 `onLoad` 方法中优先检查保存的页面状态：

```javascript
onLoad(options) {
  // 首先检查是否有保存的页面状态（登录后返回）
  const savedPageState = getPageState()
  if (savedPageState && savedPageState.pageParams) {
    this.activityId = savedPageState.pageParams.activityId
    this.inviterUserId = savedPageState.pageParams.inviterUserId
    clearPageState()
  } else {
    // 使用工具函数处理scene参数
    const params = handleSceneParams(options)
    this.activityId = params.activityId
    this.inviterUserId = params.inviterUserId
  }
}
```

#### 登录检查和状态保存

在需要登录的操作中添加检查：

```javascript
// 参与活动
async joinActivity() {
  // 检查登录状态
  if (!this.mainStore.isLogin) {
    this.handleRequireLogin()
    return
  }
  // ... 原有逻辑
}

// 处理需要登录的情况
handleRequireLogin() {
  const pageParams = {
    activityId: this.activityId,
    inviterUserId: this.inviterUserId
  }
  
  handleLoginFailure({
    returnUrl: '/pages/components/pages/activity/detail',
    pageParams: pageParams,
    source: 'activity_detail'
  })
}
```

### 4. 登录页面改进

#### 登录成功后的智能跳转

```javascript
// 处理登录成功后的页面恢复
const handleLoginSuccess = () => {
  const savedPageState = getPageState()
  
  if (savedPageState && savedPageState.returnUrl) {
    // 构建完整的URL，包含参数
    let targetUrl = savedPageState.returnUrl
    if (savedPageState.pageParams) {
      const params = new URLSearchParams()
      Object.keys(savedPageState.pageParams).forEach(key => {
        if (savedPageState.pageParams[key] !== null) {
          params.append(key, savedPageState.pageParams[key])
        }
      })
      if (params.toString()) {
        targetUrl += '?' + params.toString()
      }
    }
    
    clearPageState()
    
    // 跳转到目标页面
    uni.redirectTo({
      url: targetUrl,
      fail: () => router.back()
    })
  } else {
    router.back()
  }
}
```

## 完整流程

1. **用户扫码进入活动页面**
   - 解析scene参数获取 `activityId` 和 `inviterUserId`
   - 显示活动信息

2. **用户点击需要登录的操作**
   - 检查登录状态
   - 如未登录，调用 `handleRequireLogin()`
   - 保存当前页面URL和参数到本地存储
   - 跳转到登录页面

3. **用户完成登录**
   - 登录成功后调用 `handleLoginSuccess()`
   - 检查是否有保存的页面状态
   - 如有，构建完整URL并跳转
   - 如无，使用默认返回逻辑

4. **返回活动页面**
   - `onLoad` 方法优先检查保存的状态
   - 恢复 `activityId` 和 `inviterUserId`
   - 清除保存的状态
   - 正常加载页面内容

## 测试

创建了测试页面 `pages/test/login-flow-test.vue` 用于验证整个流程：

- 模拟设置活动参数
- 测试登录流程
- 查看页面状态保存和恢复
- 验证参数传递的完整性

## 优势

1. **无缝体验**：用户登录后能够准确返回原页面
2. **参数保持**：完整保持邀请人信息和活动ID
3. **容错性强**：如果没有保存状态，回退到默认行为
4. **扩展性好**：可以轻松扩展到其他需要登录的页面

## 注意事项

1. 页面状态保存在本地存储中，重启应用后仍然有效
2. 包含时间戳，可以用于状态过期检查
3. 支持多种参数来源（URL、scene、launch等）
4. 兼容现有的登录流程，不影响其他页面
