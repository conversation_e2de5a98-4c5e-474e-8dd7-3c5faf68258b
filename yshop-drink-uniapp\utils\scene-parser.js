/**
 * 微信小程序场景参数解析工具
 * 处理二维码扫描、小程序码等场景下的参数解析
 */

/**
 * 解析场景参数
 * @param {Object} options - 页面onLoad接收的参数
 * @returns {Object} 解析后的参数对象
 */
export const handleSceneParams = (options = {}) => {
  console.log('开始解析场景参数:', options)
  
  const result = {
    activityId: null,
    inviterUserId: null,
    source: 'unknown'
  }
  
  // 1. 优先检查直接传入的参数
  if (options.id || options.activityId) {
    result.activityId = options.id || options.activityId
    result.source = 'direct_params'
    console.log('从直接参数获取activityId:', result.activityId)
  }
  
  if (options.inviter || options.inviterUserId) {
    result.inviterUserId = options.inviter || options.inviterUserId
    if (result.source === 'unknown') {
      result.source = 'direct_params'
    }
    console.log('从直接参数获取inviterUserId:', result.inviterUserId)
  }
  
  // 2. 处理scene参数（二维码扫描）
  if (options.scene) {
    console.log('检测到scene参数:', options.scene)
    const sceneParams = parseSceneString(options.scene)
    
    if (sceneParams.activityId && !result.activityId) {
      result.activityId = sceneParams.activityId
      result.source = 'qr_code'
    }
    
    if (sceneParams.inviter && !result.inviterUserId) {
      result.inviterUserId = sceneParams.inviter
      if (result.source === 'unknown') {
        result.source = 'qr_code'
      }
    }
    
    console.log('从scene参数解析结果:', sceneParams)
  }
  
  // 3. 处理启动参数（分享等）
  if (options.query) {
    console.log('检测到query参数:', options.query)
    try {
      const queryParams = typeof options.query === 'string' 
        ? JSON.parse(options.query) 
        : options.query
      
      if (queryParams.activityId && !result.activityId) {
        result.activityId = queryParams.activityId
        result.source = 'share_link'
      }
      
      if (queryParams.inviter && !result.inviterUserId) {
        result.inviterUserId = queryParams.inviter
        if (result.source === 'unknown') {
          result.source = 'share_link'
        }
      }
    } catch (error) {
      console.error('解析query参数失败:', error)
    }
  }
  
  // 确保返回的ID是数字类型
  if (result.activityId) {
    result.activityId = parseInt(result.activityId)
  }
  if (result.inviterUserId) {
    result.inviterUserId = parseInt(result.inviterUserId)
  }
  
  console.log('最终解析结果:', result)
  return result
}

/**
 * 解析scene字符串
 * 支持多种编码格式：
 * - activityId=2&inviter=24
 * - activityId%3D2%26inviter%3D24
 * - activityId%253D2%2526inviter%253D24
 */
export const parseSceneString = (scene) => {
  if (!scene) {
    return {}
  }
  
  console.log('解析scene字符串:', scene)
  
  try {
    // 尝试多次URL解码，处理多重编码的情况
    let decodedScene = scene
    let previousScene = ''
    let attempts = 0
    const maxAttempts = 5
    
    // 循环解码直到没有变化或达到最大尝试次数
    while (decodedScene !== previousScene && attempts < maxAttempts) {
      previousScene = decodedScene
      try {
        decodedScene = decodeURIComponent(decodedScene)
        attempts++
        console.log(`解码第${attempts}次:`, decodedScene)
      } catch (decodeError) {
        console.log('解码停止，当前结果:', decodedScene)
        break
      }
    }
    
    // 解析参数
    const params = {}
    const pairs = decodedScene.split('&')
    
    pairs.forEach(pair => {
      const [key, value] = pair.split('=')
      if (key && value) {
        params[key.trim()] = value.trim()
      }
    })
    
    console.log('scene解析结果:', params)
    return params
    
  } catch (error) {
    console.error('解析scene参数失败:', error)
    return {}
  }
}

/**
 * 生成scene参数字符串
 * @param {Object} params - 参数对象
 * @returns {String} 编码后的scene字符串
 */
export const generateSceneString = (params = {}) => {
  const pairs = []
  
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      pairs.push(`${key}=${params[key]}`)
    }
  })
  
  const sceneString = pairs.join('&')
  console.log('生成scene字符串:', sceneString)
  
  // 进行URL编码
  return encodeURIComponent(sceneString)
}

/**
 * 验证参数有效性
 * @param {Object} params - 参数对象
 * @returns {Object} 验证结果
 */
export const validateParams = (params) => {
  const result = {
    isValid: true,
    errors: []
  }
  
  if (params.activityId) {
    const activityId = parseInt(params.activityId)
    if (isNaN(activityId) || activityId <= 0) {
      result.isValid = false
      result.errors.push('activityId必须是正整数')
    }
  }
  
  if (params.inviterUserId) {
    const inviterUserId = parseInt(params.inviterUserId)
    if (isNaN(inviterUserId) || inviterUserId <= 0) {
      result.isValid = false
      result.errors.push('inviterUserId必须是正整数')
    }
  }
  
  return result
}

/**
 * 获取当前页面的完整URL（包含参数）
 * @param {String} basePath - 基础路径
 * @param {Object} params - 参数对象
 * @returns {String} 完整URL
 */
export const buildPageUrl = (basePath, params = {}) => {
  const validParams = {}
  
  // 过滤有效参数
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      validParams[key] = params[key]
    }
  })
  
  if (Object.keys(validParams).length === 0) {
    return basePath
  }
  
  const queryString = Object.keys(validParams)
    .map(key => `${key}=${encodeURIComponent(validParams[key])}`)
    .join('&')
  
  return `${basePath}?${queryString}`
}

export default {
  handleSceneParams,
  parseSceneString,
  generateSceneString,
  validateParams,
  buildPageUrl
}
