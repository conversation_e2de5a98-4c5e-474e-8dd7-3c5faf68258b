<template>
  <view class="container">
    <view class="header">
      <text class="title">Scene参数解析测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试输入</text>
      <input 
        v-model="testScene" 
        placeholder="输入scene参数进行测试"
        class="input"
      />
      <button @click="testParsing" class="test-btn">测试解析</button>
    </view>
    
    <view class="result-section">
      <text class="section-title">解析结果</text>
      <view class="result-item">
        <text class="label">Activity ID:</text>
        <text class="value">{{ result.activityId || '未获取' }}</text>
      </view>
      <view class="result-item">
        <text class="label">Inviter ID:</text>
        <text class="value">{{ result.inviterUserId || '未获取' }}</text>
      </view>
      <view class="result-item">
        <text class="label">来源:</text>
        <text class="value">{{ result.source || '未知' }}</text>
      </view>
    </view>
    
    <view class="preset-section">
      <text class="section-title">预设测试用例</text>
      <button 
        v-for="(testCase, index) in testCases" 
        :key="index"
        @click="useTestCase(testCase)"
        class="preset-btn"
      >
        {{ testCase.name }}
      </button>
    </view>
    
    <view class="log-section">
      <text class="section-title">控制台日志</text>
      <view class="log-content">
        <text class="log-text">请查看开发者工具控制台获取详细日志</text>
      </view>
    </view>
  </view>
</template>

<script>
import { testSceneParsing, handleSceneParams } from '@/utils/scene-parser'

export default {
  data() {
    return {
      testScene: '',
      result: {
        activityId: null,
        inviterUserId: null,
        source: null
      },
      testCases: [
        {
          name: '标准格式',
          scene: 'activityId=2&inviter=24'
        },
        {
          name: 'URL编码格式',
          scene: 'activityId%3D2%26inviter%3D24'
        },
        {
          name: '双重编码格式',
          scene: 'activityId%253D2%2526inviter%253D24'
        },
        {
          name: '只有活动ID',
          scene: 'activityId=5'
        },
        {
          name: '复杂参数',
          scene: 'activityId=10&inviter=100&extra=test'
        }
      ]
    }
  },
  
  onLoad(options) {
    console.log('测试页面 onLoad options:', options)
    
    // 如果有scene参数，直接测试
    if (options.scene) {
      this.testScene = decodeURIComponent(options.scene)
      this.testParsing()
    }
    
    // 测试handleSceneParams函数
    const params = handleSceneParams(options)
    console.log('handleSceneParams结果:', params)
  },
  
  methods: {
    testParsing() {
      if (!this.testScene) {
        uni.showToast({
          title: '请输入测试参数',
          icon: 'error'
        })
        return
      }
      
      console.log('=== 开始测试scene参数解析 ===')
      
      // 使用测试函数
      const parseResult = testSceneParsing(this.testScene)
      
      // 模拟options对象测试handleSceneParams
      const mockOptions = {
        scene: encodeURIComponent(this.testScene)
      }
      const handleResult = handleSceneParams(mockOptions)
      
      this.result = {
        activityId: handleResult.activityId,
        inviterUserId: handleResult.inviterUserId,
        source: handleResult.source
      }
      
      console.log('最终结果:', this.result)
      
      uni.showToast({
        title: '解析完成，查看控制台',
        icon: 'success'
      })
    },
    
    useTestCase(testCase) {
      this.testScene = testCase.scene
      this.testParsing()
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section, .result-section, .preset-section, .log-section {
  background-color: white;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.test-btn, .preset-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.preset-btn {
  background-color: #34c759;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.value {
  font-size: 28rpx;
  color: #333;
}

.log-content {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
}

.log-text {
  font-size: 24rpx;
  color: #666;
}
</style>
