package co.yixiang.yshop.module.order.service.activity;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import co.yixiang.yshop.module.order.controller.app.activity.vo.AppActivityShareReqVO;
import co.yixiang.yshop.module.order.controller.app.activity.vo.AppActivityShareRespVO;
import co.yixiang.yshop.module.order.controller.app.activity.vo.AppActivityPosterRespVO;
import co.yixiang.yshop.module.order.dal.dataobject.activity.ActivityDO;
import co.yixiang.yshop.module.order.dal.dataobject.activity.ActivityUserDO;
import co.yixiang.yshop.module.order.dal.mysql.activity.ActivityMapper;
import co.yixiang.yshop.module.order.dal.mysql.activity.ActivityUserMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import co.yixiang.yshop.module.desk.service.qrcode.QrcodeService;
import co.yixiang.yshop.module.desk.dal.dataobject.qrcode.QrcodeDO;
import co.yixiang.yshop.module.infra.service.file.FileService;
import co.yixiang.yshop.module.member.api.user.MemberUserApi;
import co.yixiang.yshop.module.member.api.user.dto.MemberUserRespDTO;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static co.yixiang.yshop.framework.common.exception.util.ServiceExceptionUtil.exception;
import static co.yixiang.yshop.module.store.enums.ErrorCodeConstants.*;

/**
 * 分销活动分享 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ActivityShareServiceImpl implements ActivityShareService {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private ActivityUserMapper activityUserMapper;

    @Resource
    private QrcodeService qrcodeService;

    @Resource
    private FileService fileService;

    @Resource
    private MemberUserApi memberUserApi;

    private static final String ACTIVITY_BASE_URL = "https://yqhua.com/h5/#/";
    private static final String MINI_PROGRAM_PAGE = "pages/components/pages/activity/detail";

    @Override
    public AppActivityShareRespVO generateShareInfo(Long userId, AppActivityShareReqVO shareReqVO) {
        // 校验活动是否存在
        ActivityDO activity = activityMapper.selectById(shareReqVO.getActivityId());
        if (activity == null) {
            throw exception(SHOP_DUE_LABEL_NOT_EXISTS); // 需要定义活动不存在的错误码
        }

        AppActivityShareRespVO shareInfo = new AppActivityShareRespVO();
        
        // 生成邀请码
        String inviteCode = generateInviteCode(userId, shareReqVO.getActivityId());
        shareInfo.setInviteCode(inviteCode);
        
        // 生成分享链接
        String shareUrl = ACTIVITY_BASE_URL +  "?inviter=" + userId + "&code=" + inviteCode;
        shareInfo.setShareUrl(shareUrl);
        
        // 设置分享内容
        shareInfo.setShareTitle(activity.getName());
        shareInfo.setShareDesc(activity.getContent());
        shareInfo.setShareImage(activity.getImage());
        
        // 设置小程序页面路径
        shareInfo.setMiniProgramPath(MINI_PROGRAM_PAGE + "?id=" + shareReqVO.getActivityId() + "&inviter=" + userId);
        
        // 根据分享类型生成对应内容
        switch (shareReqVO.getShareType()) {
            case 3: // 生成海报
                String posterUrl = generateActivityPoster(userId, shareReqVO.getActivityId());
                shareInfo.setPosterUrl(posterUrl);
                break;
            default:
                // 生成小程序二维码
                String qrCodeUrl = generateMiniProgramQrCode(userId, shareReqVO.getActivityId());
                shareInfo.setQrCodeUrl(qrCodeUrl);
                break;
        }
        
        return shareInfo;
    }

    @Override
    public String generateInviteCode(Long userId, Long activityId) {
        // 生成邀请码：用户ID + 活动ID + 随机字符串的组合
        String randomStr = IdUtil.fastSimpleUUID().substring(0, 6).toUpperCase();
        return "ACT" + activityId + "U" + userId + randomStr;
    }

    @Override
    public String generateActivityPoster(Long userId, Long activityId) {
        try {
            // 先查询用户是否已有海报缓存
            ActivityUserDO activityUser = getOrCreateActivityUser(userId, activityId);
            if (activityUser != null && StrUtil.isNotEmpty(activityUser.getPosterUrl())) {
                log.info("使用缓存的海报，activityId: {}, userId: {}, posterUrl: {}", activityId, userId, activityUser.getPosterUrl());
                return activityUser.getPosterUrl();
            }

            // 获取活动信息
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                log.error("活动不存在，activityId: {}", activityId);
                return null;
            }

            // 获取用户信息
            MemberUserRespDTO user = memberUserApi.getUser(userId);
            if (user == null) {
                log.error("用户不存在，userId: {}", userId);
                return null;
            }

            // 生成小程序二维码
            String scene = "activityId=" + activityId + "&inviter=" + userId;
            QrcodeDO qrcodeDO = qrcodeService.createMiniQrcode(MINI_PROGRAM_PAGE, scene, false, "release");

            // 创建海报图片
            BufferedImage posterImage = createPosterImage(activity, user, qrcodeDO);

            // 将图片转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(posterImage, "jpg", baos);
            byte[] imageBytes = baos.toByteArray();

            // 生成文件名和路径
            String fileName = "poster_activity_" + activityId + "_user_" + userId + "_" + System.currentTimeMillis() + ".jpg";
            String uploadPath = "posters/activity/" + fileName;

            // 上传到文件服务
            String fileUrl = fileService.createFile(fileName, uploadPath, imageBytes);

            // 保存海报URL到用户活动记录
            if (activityUser != null) {
                activityUser.setPosterUrl(fileUrl);
                activityUserMapper.updateById(activityUser);
                log.info("海报URL已保存到用户活动记录，activityUserId: {}", activityUser.getId());
            }

            log.info("海报生成成功，activityId: {}, userId: {}, fileUrl: {}", activityId, userId, fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("生成活动海报失败，activityId: {}, userId: {}", activityId, userId, e);
            return null;
        }
    }

    @Override
    public String generateMiniProgramQrCode(Long userId, Long activityId) {
            // 使用现有的二维码服务生成小程序码
            String scene = "activityId=" + activityId + "&inviter=" + userId;
            String page = MINI_PROGRAM_PAGE;

            // 调用二维码服务生成小程序码
            QrcodeDO qrcodeDO = qrcodeService.createMiniQrcode(page, scene, false, "release");
            if (qrcodeDO != null && qrcodeDO.getSrc() != null) {
                // 返回相对路径，前端可以拼接域名
                return qrcodeDO.getSrc();
            }
            throw exception(ErrorCodeConstants.CREATE_QRCODE_FAIL);

    }

    @Override
    public AppActivityPosterRespVO generateActivityPosterInfo(Long userId, Long activityId) {
        try {
            // 获取活动信息
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity == null) {
                log.error("活动不存在，activityId: {}", activityId);
                return null;
            }

            // 获取或创建用户活动记录
            ActivityUserDO activityUser = getOrCreateActivityUser(userId, activityId);
            if (activityUser == null) {
                log.error("无法创建用户活动记录，activityId: {}, userId: {}", activityId, userId);
                return null;
            }

            // 生成海报URL（会使用缓存）
            String posterUrl = generateActivityPoster(userId, activityId);
            if (posterUrl == null) {
                return null;
            }

            // 使用缓存的邀请码，如果没有则生成新的
            String inviteCode = activityUser.getInviteCode();
            if (StrUtil.isEmpty(inviteCode)) {
                inviteCode = generateInviteCode(userId, activityId);
                activityUser.setInviteCode(inviteCode);
                activityUserMapper.updateById(activityUser);
            }

            // 生成分享链接
            String shareUrl = ACTIVITY_BASE_URL + activityId + "?inviter=" + userId + "&code=" + inviteCode;

            // 构建响应对象
            AppActivityPosterRespVO posterInfo = new AppActivityPosterRespVO();
            posterInfo.setPosterUrl(posterUrl);
            posterInfo.setShareUrl(shareUrl);
            posterInfo.setActivityId(activityId);
            posterInfo.setActivityName(activity.getName());
            posterInfo.setInviteCode(inviteCode);

            return posterInfo;

        } catch (Exception e) {
            log.error("生成活动海报信息失败，activityId: {}, userId: {}", activityId, userId, e);
            return null;
        }
    }

    /**
     * 创建海报图片
     */
    private BufferedImage createPosterImage(ActivityDO activity, MemberUserRespDTO user, QrcodeDO qrcodeDO) throws IOException {
        // 海报尺寸
        int width = 750;
        int height = 1334;

        // 创建画布
        BufferedImage posterImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = posterImage.createGraphics();

        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 背景渐变色
        GradientPaint gradient = new GradientPaint(0, 0, new Color(102, 126, 234), 0, height, new Color(118, 75, 162));
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, width, height);

        // 绘制白色内容区域
        g2d.setColor(Color.WHITE);
        int contentX = 40;
        int contentY = 100;
        int contentWidth = width - 80;
        int contentHeight = height - 200;
        g2d.fillRoundRect(contentX, contentY, contentWidth, contentHeight, 20, 20);

        // 绘制活动标题
        // g2d.setColor(new Color(51, 51, 51));
        // g2d.setFont(new Font("微软雅黑", Font.BOLD, 36));
        // FontMetrics titleMetrics = g2d.getFontMetrics();
        // String title = activity.getName();
        // if (title.length() > 15) {
        //     title = title.substring(0, 15) + "...";
        // }
        // int titleX = contentX + (contentWidth - titleMetrics.stringWidth(title)) / 2;
        // g2d.drawString(title, titleX, contentY + 80);

        // 绘制活动描述
        // g2d.setColor(new Color(102, 102, 102));
        // g2d.setFont(new Font("微软雅黑", Font.PLAIN, 24));
        // String desc = activity.getContent();
        // if (desc != null && desc.length() > 30) {
        //     desc = desc.substring(0, 30) + "...";
        // }
        // if (desc != null) {
        //     FontMetrics descMetrics = g2d.getFontMetrics();
        //     int descX = contentX + (contentWidth - descMetrics.stringWidth(desc)) / 2;
        //     g2d.drawString(desc, descX, contentY + 140);
        // }

        // 绘制邀请人信息
        // g2d.setColor(new Color(102, 126, 234));
        // g2d.setFont(new Font("微软雅黑", Font.BOLD, 28));
        // String inviterText = "邀请人：" + (user.getNickname() != null ? user.getNickname() : "用户" + user.getId());
        // FontMetrics inviterMetrics = g2d.getFontMetrics();
        // int inviterX = contentX + (contentWidth - inviterMetrics.stringWidth(inviterText)) / 2;
        // g2d.drawString(inviterText, inviterX, contentY + 200);

        // 绘制分割线
        // g2d.setColor(new Color(240, 240, 240));
        // g2d.fillRect(contentX + 40, contentY + 240, contentWidth - 80, 2);

        // 绘制二维码
        if (qrcodeDO != null && StrUtil.isNotEmpty(qrcodeDO.getFullPath())) {
            try {
                // 从文件服务获取二维码图片
                BufferedImage qrImage = loadQrCodeImage(qrcodeDO);
                if (qrImage != null) {
                    int qrSize = 280;
                    int qrX = contentX + (contentWidth - qrSize) / 2;
                    int qrY = contentY + 300;

                    // 绘制白色背景
                    g2d.setColor(Color.WHITE);
                    g2d.fillRoundRect(qrX - 15, qrY - 15, qrSize + 30, qrSize + 30, 15, 15);

                    // 绘制边框
                    g2d.setColor(new Color(230, 230, 230));
                    g2d.drawRoundRect(qrX - 15, qrY - 15, qrSize + 30, qrSize + 30, 15, 15);

                    // 绘制二维码图片
                    g2d.drawImage(qrImage, qrX, qrY, qrSize, qrSize, null);

                    // 绘制二维码说明文字
                    g2d.setColor(new Color(102, 102, 102));
                    g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
                    String qrText = "长按识别二维码参与活动";
                    FontMetrics qrMetrics = g2d.getFontMetrics();
                    int qrTextX = contentX + (contentWidth - qrMetrics.stringWidth(qrText)) / 2;
                    g2d.drawString(qrText, qrTextX, qrY + qrSize + 40);
                } else {
                    // 如果无法加载二维码图片，绘制占位区域
                    drawQrCodePlaceholder(g2d, contentX, contentY, contentWidth);
                }

            } catch (Exception e) {
                log.warn("绘制二维码失败", e);
                // 绘制占位区域
                drawQrCodePlaceholder(g2d, contentX, contentY, contentWidth);
            }
        } else {
            // 绘制占位区域
            drawQrCodePlaceholder(g2d, contentX, contentY, contentWidth);
        }

        g2d.dispose();
        return posterImage;
    }

    /**
     * 从文件服务加载二维码图片
     */
    private BufferedImage loadQrCodeImage(QrcodeDO qrcodeDO) {
        try {
            if (qrcodeDO.getConfigId() == null || StrUtil.isEmpty(qrcodeDO.getSrc())) {
                log.warn("二维码配置信息不完整，configId: {}, src: {}", qrcodeDO.getConfigId(), qrcodeDO.getSrc());
                return null;
            }

            // 获取二维码文件内容
            byte[] qrCodeBytes = fileService.getFileContent(qrcodeDO.getConfigId(), qrcodeDO.getSrc());
            if (qrCodeBytes != null && qrCodeBytes.length > 0) {
                // 将字节数组转换为BufferedImage
                BufferedImage qrImage = ImageIO.read(new java.io.ByteArrayInputStream(qrCodeBytes));
                if (qrImage != null) {
                    log.info("成功加载二维码图片，尺寸: {}x{}", qrImage.getWidth(), qrImage.getHeight());
                    return qrImage;
                } else {
                    log.warn("二维码图片解析失败");
                }
            } else {
                log.warn("二维码文件内容为空");
            }
        } catch (Exception e) {
            log.error("加载二维码图片失败，configId: {}, src: {}", qrcodeDO.getConfigId(), qrcodeDO.getSrc(), e);
        }
        return null;
    }

    /**
     * 绘制二维码占位区域
     */
    private void drawQrCodePlaceholder(Graphics2D g2d, int contentX, int contentY, int contentWidth) {
        // 绘制二维码占位区域
        int qrSize = 180;
        int qrX = contentX + (contentWidth - qrSize) / 2;
        int qrY = contentY + 300;

        // 绘制白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRoundRect(qrX - 15, qrY - 15, qrSize + 30, qrSize + 30, 15, 15);

        // 绘制边框
        g2d.setColor(new Color(230, 230, 230));
        g2d.drawRoundRect(qrX - 15, qrY - 15, qrSize + 30, qrSize + 30, 15, 15);

        // 绘制占位区域
        g2d.setColor(new Color(248, 248, 248));
        g2d.fillRoundRect(qrX, qrY, qrSize, qrSize, 10, 10);

        // 绘制二维码提示文字
        g2d.setColor(new Color(153, 153, 153));
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 20));
        String qrText = "二维码加载中...";
        FontMetrics qrMetrics = g2d.getFontMetrics();
        int qrTextX = qrX + (qrSize - qrMetrics.stringWidth(qrText)) / 2;
        g2d.drawString(qrText, qrTextX, qrY + qrSize / 2);

        // 绘制说明文字
        g2d.setColor(new Color(102, 102, 102));
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
        String descText = "长按识别二维码参与活动";
        FontMetrics descMetrics = g2d.getFontMetrics();
        int descTextX = contentX + (contentWidth - descMetrics.stringWidth(descText)) / 2;
        g2d.drawString(descText, descTextX, qrY + qrSize + 40);
    }

    /**
     * 获取或创建用户活动记录
     */
    private ActivityUserDO getOrCreateActivityUser(Long userId, Long activityId) {
        // 先查询是否已存在
        ActivityUserDO activityUser = activityUserMapper.selectOne(
            new LambdaQueryWrapper<ActivityUserDO>()
                .eq(ActivityUserDO::getUid, userId)
                .eq(ActivityUserDO::getYshopActivityId, activityId)
        );

        if (activityUser == null) {
            // 如果不存在，创建新记录
            ActivityDO activity = activityMapper.selectById(activityId);
            if (activity != null) {
                activityUser = new ActivityUserDO();
                activityUser.setYshopActivityId(activityId);
                activityUser.setUid(userId);
                activityUser.setStatus(0); // 0-未完成
                activityUser.setNumber(activity.getNumber()); // 需要完成的数量
                activityUser.setAlreadyNumber(0); // 已完成数量

                // 生成邀请码
                String inviteCode = generateInviteCode(userId, activityId);
                activityUser.setInviteCode(inviteCode);

                activityUserMapper.insert(activityUser);
                log.info("创建新的用户活动记录，userId: {}, activityId: {}, activityUserId: {}", userId, activityId, activityUser.getId());
            }
        }

        return activityUser;
    }

    @Override
    public boolean clearActivityPosterCache(Long userId, Long activityId) {
        try {
            // 查询用户活动记录
            ActivityUserDO activityUser = activityUserMapper.selectOne(
                new LambdaQueryWrapper<ActivityUserDO>()
                    .eq(ActivityUserDO::getUid, userId)
                    .eq(ActivityUserDO::getYshopActivityId, activityId)
            );

            if (activityUser != null && StrUtil.isNotEmpty(activityUser.getPosterUrl())) {
                // 清除海报URL
                activityUser.setPosterUrl(null);
                activityUserMapper.updateById(activityUser);

                log.info("清除用户活动海报缓存成功，userId: {}, activityId: {}", userId, activityId);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("清除用户活动海报缓存失败，userId: {}, activityId: {}", userId, activityId, e);
            return false;
        }
    }

}
