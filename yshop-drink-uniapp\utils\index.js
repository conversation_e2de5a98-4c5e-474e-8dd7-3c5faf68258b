import stringify from '@/utils/querystring'

import router from './router'
import cookie from './cookie'
import { useMainStore } from '@/store/store'


/**
 * 保存当前页面状态到本地存储
 * @param {object} pageState - 页面状态对象
 */
export const savePageState = (pageState) => {
  try {
    uni.setStorageSync('pageState', pageState)
    console.log('页面状态已保存:', pageState)
  } catch (error) {
    console.error('保存页面状态失败:', error)
  }
}

/**
 * 获取保存的页面状态
 * @returns {object|null} 页面状态对象
 */
export const getPageState = () => {
  try {
    const pageState = uni.getStorageSync('pageState')
    console.log('获取页面状态:', pageState)
    return pageState || null
  } catch (error) {
    console.error('获取页面状态失败:', error)
    return null
  }
}

/**
 * 清除保存的页面状态
 */
export const clearPageState = () => {
  try {
    uni.removeStorageSync('pageState')
    console.log('页面状态已清除')
  } catch (error) {
    console.error('清除页面状态失败:', error)
  }
}

/**
 * 处理登录失败，跳转到登录页面前保存当前页面状态
 * @param {object} options - 可选参数
 * @param {string} options.returnUrl - 登录成功后要返回的页面URL
 * @param {object} options.pageParams - 页面参数（如activityId, inviterUserId等）
 * @param {string} options.source - 参数来源（url, scene, launch等）
 */
export const handleLoginFailure = (options = {}) => {
  // 清除用户信息
  uni.removeStorageSync('userinfo');
  uni.removeStorageSync('accessToken');
  const main = useMainStore()
  main.SET_MEMBER({});
  main.SET_TOKEN('');

  // 保存当前页面状态
  if (options.returnUrl || options.pageParams) {
    const pageState = {
      returnUrl: options.returnUrl,
      pageParams: options.pageParams,
      source: options.source || 'unknown',
      timestamp: Date.now()
    }
    savePageState(pageState)
  }

  // #ifdef H5
  if(isWeixin()){
	  uni.switchTab({
	  	url: '/pages/index/index'
	  })

	  return
   }
  // #endif

  // 使用 navigateTo 而不是 redirectTo，保持页面栈
  uni.navigateTo({
	url: '/pages/components/pages/login/login',
	fail: (error) => {
	  console.error('跳转登录页面失败:', error)
	  // 如果 navigateTo 失败，可能是页面栈满了，使用 redirectTo
	  uni.redirectTo({
		url: '/pages/components/pages/login/login',
	  })
	}
  })
}

export function parseUrl(location) {
  if (typeof location === 'string') return location
  const { url, query } = location

  const queryStr = stringify(query)

  if (!queryStr) {
    return url
  }

  return `${url}?${queryStr}`
}

const toAuth = () => {
  uni.showToast({
    title: '暂未开放',
    icon: 'none',
    duration: 2000,
  })
}

export default {
  install: (app, options) => {
    // 在这里编写插件代码
    // 注入一个全局可用的 $translate() 方法
    app.config.globalProperties.$yrouter = router
    app.config.globalProperties.$cookie = cookie
    app.config.globalProperties.$toAuth = toAuth
    app.config.globalProperties.$onClickLeft = () => {
      router.back()
	  //uni.navigateBack()
	  //const mypage = getCurrentPages()
	  //console.log('mypage:',mypage)
    }

    // #ifdef H5
    app.config.globalProperties.$platform = 'h5'
    // #endif

    // #ifdef APP-PLUS
    // app端
    app.config.globalProperties.$platform = 'app'
    // #endif

    // #ifdef MP-WEIXIN
    app.config.globalProperties.$platform = 'routine'
    // #endif
  },
}
