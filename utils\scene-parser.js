/**
 * 微信小程序scene参数解析工具
 * 用于处理小程序码扫描时的参数传递
 */

import { urlDecode } from '@/utils/util'

/**
 * 解析scene参数
 * @param {string} sceneStr - scene参数字符串
 * @returns {object} 解析后的参数对象
 */
export function parseSceneParams(sceneStr) {
  if (!sceneStr) return {}
  
  try {
    console.log('原始scene参数:', sceneStr)
    
    // 使用现有的urlDecode工具函数
    // 需要确保参数格式为 ?activityId=2&inviter=24
    const queryStr = sceneStr.startsWith('?') ? sceneStr : '?' + sceneStr
    const result = urlDecode(queryStr) || {}
    
    console.log('urlDecode解析结果:', result)
    return result
  } catch (error) {
    console.error('urlDecode解析失败，使用备用方法:', error)
    
    // 备用解析方法
    const params = {}
    try {
      const pairs = sceneStr.split('&')
      pairs.forEach(pair => {
        const [key, value] = pair.split('=')
        if (key && value) {
          params[key] = decodeURIComponent(value)
        }
      })
      console.log('备用方法解析结果:', params)
      return params
    } catch (backupError) {
      console.error('备用解析方法也失败:', backupError)
      return {}
    }
  }
}

/**
 * 处理微信小程序的scene参数
 * @param {object} options - onLoad的options参数
 * @returns {object} 包含解析后参数的对象
 */
export function handleSceneParams(options) {
  const result = {
    activityId: null,
    inviterUserId: null,
    source: 'unknown'
  }
  
  // 处理普通URL参数
  if (options.id) {
    result.activityId = options.id
    result.inviterUserId = options.inviter || null
    result.source = 'url'
    console.log('从URL参数获取:', result)
    return result
  }
  
  // 处理微信小程序码scene参数
  if (options.scene) {
    try {
      // 解码scene参数
      const sceneStr = decodeURIComponent(options.scene)
      console.log('Scene参数:', sceneStr)
      
      // 解析scene参数
      const sceneParams = parseSceneParams(sceneStr)
      console.log('解析后的scene参数:', sceneParams)
      
      if (sceneParams.activityId) {
        result.activityId = sceneParams.activityId
        result.inviterUserId = sceneParams.inviter || null
        result.source = 'scene'
      }
    } catch (error) {
      console.error('解析scene参数失败:', error)
    }
  }
  
  // #ifdef MP-WEIXIN
  // 通过wx.getLaunchOptionsSync()获取启动参数
  try {
    const launchOptions = wx.getLaunchOptionsSync()
    console.log('启动参数:', launchOptions)
    
    // 场景值1047表示扫描小程序码
    if (launchOptions.scene === 1047 && launchOptions.query && launchOptions.query.scene) {
      const sceneStr = decodeURIComponent(launchOptions.query.scene)
      const sceneParams = parseSceneParams(sceneStr)
      
      if (sceneParams.activityId && !result.activityId) {
        result.activityId = sceneParams.activityId
        result.inviterUserId = sceneParams.inviter || null
        result.source = 'launch'
      }
    }
  } catch (error) {
    console.error('获取启动参数失败:', error)
  }
  // #endif
  
  console.log('最终解析结果:', result)
  return result
}

/**
 * 处理小程序热启动时的scene参数
 * @returns {object} 包含解析后参数的对象
 */
export function handleHotStartSceneParams() {
  const result = {
    activityId: null,
    inviterUserId: null,
    source: 'hotstart'
  }

  // #ifdef MP-WEIXIN
  try {
    // 在小程序环境中，getCurrentPages是全局函数
    // eslint-disable-next-line no-undef
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    if (currentPage && currentPage.options && currentPage.options.scene) {
      const sceneStr = decodeURIComponent(currentPage.options.scene)
      console.log('热启动scene参数:', sceneStr)

      const sceneParams = parseSceneParams(sceneStr)
      console.log('热启动解析后的scene参数:', sceneParams)

      if (sceneParams.activityId) {
        result.activityId = sceneParams.activityId
        result.inviterUserId = sceneParams.inviter || null
      }
    }
  } catch (error) {
    console.error('处理热启动scene参数失败:', error)
  }
  // #endif

  return result
}

/**
 * 测试scene参数解析
 * @param {string} testScene - 测试用的scene参数
 */
export function testSceneParsing(testScene) {
  console.log('=== Scene参数解析测试 ===')
  console.log('测试参数:', testScene)
  
  const result = parseSceneParams(testScene)
  console.log('解析结果:', result)
  
  return result
}

// 导出常用的场景值
export const SCENE_VALUES = {
  QR_CODE: 1047, // 扫描小程序码
  SHARE_CARD: 1008, // 群聊会话中的小程序消息卡片
  SHARE_TIMELINE: 1036, // App分享消息卡片
  MINI_PROGRAM: 1037, // 小程序打开小程序
  BACK_FROM_MINI_PROGRAM: 1038, // 从另一个小程序返回
}
