<template>
	<layout>
		<!-- #ifdef MP-WEIXIN -->
		<uv-navbar
		  :fixed="false"
		  :title="title"
		  left-arrow
		  @leftClick="$onClickLeft"
		/>
		<!-- #endif -->
		<view class="wrap">
		<view class="top"></view>
		<view class="content">
			<view class="title">欢迎登录</view>
			
			<input class="u-border-bottom" type="number" v-model="mobile" placeholder="请输入手机号" />
			<view class="tips">未注册的手机号验证后自动创建账号</view>

			
			<view class="flex align-center">
				<view style="width: 50%;">
					<input style=""  class="u-border-bottom" type="number" v-model="captcha" placeholder="请输入验证码" />
				</view>
				<view style="width: 40%;">
					<button  @tap="getCaptcha" :style="[captchaStyle]" class="getCaptcha">
						{{captchaText}}
						<uv-code :seconds="seconds" @end="endCaptcha" @start="startCaptcha" ref="uCode" @change="changeCapcha"></uv-code>
					<!-- 	<u-verification-code :seconds="seconds" @end="endCaptcha" @change="changeCapcha" @start="startCaptcha" ref="uCode" >
							
						</u-verification-code> -->
					</button>
				</view>
			</view>
			
			<button @tap="submit" style="" type="primary" class="login">立即登录</button>
			
		</view>
		<view class="buttom">
			<view class="loginType">
				<!-- #ifdef MP-WEIXIN -->
				<button type="primary" v-if="isChecked" size="default" class="login-btn" open-type="getPhoneNumber" @getphonenumber="loginForWechatMini">
				<!-- 	<image src="/static/images/mine/wechat.png"></image> -->
					手机号快捷登录
				</button>
				<button type="primary" v-else size="default" class="login-btn" @tap="check">
				<!-- 	<image src="/static/images/mine/wechat.png"></image> -->
					手机号快捷登录
				</button>
				<!-- #endif -->
				
				<!-- #ifdef H5 -->
				 <button type="primary"  size="default" class="login-btn" @tap="wxLogin">
					 微信登录
				 </button>
				<!-- #endif -->
			</view>
			<view class="hint">
			<!-- 	<label class="label"> -->
					<radio value="isChecked" @tap.stop="onChange" />
					我已经阅读并遵守
					<text class="link" @tap="serv(29,'用户协议')">《用户协议》</text>与
						<text class="link"  @tap="serv(30,'隐私政策')">《隐私政策》</text>
			<!-- 	</label> -->
			</view>
		</view>
		<uv-toast ref="uToast"></uv-toast>
	</view>
	</layout>
</template>

<script setup>
import {
  ref,
  computed
} from 'vue'
import { onLoad,onShow} from '@dcloudio/uni-app'
import { useMainStore } from '@/store/store'
import {
  userAuthSession,
  userLoginForWechatMini,
  smsSend,
  userLogin
} from '@/api/auth'
import * as util  from '@/utils/util'
import { mobile as testMobible } from '@/uni_modules/uv-ui-tools/libs/function/test'
import cookie from '@/utils/cookie'
import router from '@/utils/router'
import { APP_ID } from '@/config'
import { getPageState, clearPageState } from '@/utils/index'
const main = useMainStore()
const title = ref('登录')
const mobile = ref('')
const captcha = ref('')
const captchaText = ref('获取验证码')	
const password = ref('')
const seconds = ref(60)
const isChecked = ref(false)
const openid = ref(main.openid)
const uToast = ref()
const uCode = ref()

// 处理登录成功后的页面恢复
const handleLoginSuccess = () => {
  const savedPageState = getPageState()

  if (savedPageState && savedPageState.returnUrl) {
    console.log('登录成功，恢复到保存的页面:', savedPageState)

    // 构建完整的URL，包含参数
    let targetUrl = savedPageState.returnUrl
    if (savedPageState.pageParams) {
      const params = new URLSearchParams()
      Object.keys(savedPageState.pageParams).forEach(key => {
        if (savedPageState.pageParams[key] !== null && savedPageState.pageParams[key] !== undefined) {
          params.append(key, savedPageState.pageParams[key])
        }
      })
      if (params.toString()) {
        targetUrl += '?' + params.toString()
      }
    }

    console.log('跳转到目标页面:', targetUrl)

    // 清除保存的状态
    clearPageState()

    // 跳转到目标页面 - 使用 navigateTo 保持页面栈
    uni.navigateTo({
      url: targetUrl,
      fail: (error) => {
        console.error('navigateTo失败，尝试redirectTo:', error)
        // 如果 navigateTo 失败，尝试 redirectTo
        uni.redirectTo({
          url: targetUrl,
          fail: (redirectError) => {
            console.error('redirectTo也失败，跳转到首页:', redirectError)
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        })
      }
    })
  } else {
    console.log('没有保存的页面状态，检查页面栈')

    // 检查当前页面栈
    const pages = getCurrentPages()
    console.log('当前页面栈长度:', pages.length)

    if (pages.length <= 1) {
      // 如果只有一页或没有页面，跳转到首页
      console.log('页面栈只有一页，跳转到首页')
      uni.switchTab({
        url: '/pages/index/index'
      })
    } else {
      // 有多页，可以安全返回
      console.log('页面栈有多页，返回上一页')
      uni.navigateBack({
        delta: 1,
        fail: (error) => {
          console.error('返回失败，跳转到首页:', error)
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }
  }
}

const captchaStyle = computed(() => {
  let style = {};
  if(mobile.value && captchaText.value == '获取验证码') {
  	style.color = "#fff";
  	style.backgroundColor = '#f9ae3d';
  }
  return style;
});

onShow(() => {
   
	// #ifdef MP-WEIXIN
	if(!openid.value){
		wechatMiniLogin();
	}
	
	// #endif
})

const wechatMiniLogin = () => {
	//this.$u.toast('登录中');
	uni.login({
		provider: 'weixin'
	}).then(async (res) => {
		let data = await userAuthSession({
			code: res.code
		});
		if (data) {
			main.SET_OPENID(data.openId)
			openid.value = data.openId
		}
	});
}

const check = () => {
	if(!isChecked.value){
		uToast.value.show({
			message: '请勾选下面协议',
			type: 'error'
		});
		return
	}
}

const loginForWechatMini = async (e) => {
	
	if (e.detail.encryptedData && e.detail.iv) {
		let data = await userLoginForWechatMini({
			encryptedData: e.detail.encryptedData,
			iv: e.detail.iv,
			openid: openid.value
		});
		if (data) {
			main.SET_MEMBER(data.userInfo);
			main.SET_TOKEN(data.accessToken);
			uToast.value.show({
				title: '登录成功',
				type: 'success'
			});
			setTimeout(function() {
				handleLoginSuccess()
			}, 2000);
		}
	}
}

const getCaptcha = async () => {
			
	if (testMobible(mobile.value) == false) {
		uToast.value.show({
			message: '手机号码格式不对',
			type: 'error'
		});
		return
	}
	
	let data = await smsSend({
		mobile: mobile.value,
		scene: 1
	});		
	if (data) {
		uCode.value.start();
		
	}
}

// 验证码开始计时	
const startCaptcha = () => {
}
// 验证码结束
const endCaptcha = () => {
	captchaText.value = '获取验证码';
}
const changeCapcha = (text)  => {
	captchaText.value = text;
}

const wxLogin = () => {
	if(!isChecked.value){
		uToast.value.show({
			message: '请勾选下面协议',
			type: 'error'
		});
		return
	}
	if(!util.isWeixin()){
		uToast.value.show({
			message: '请微信浏览器打开',
			type: 'error'
		});
		return
	}
	const appid = APP_ID;
	location.href = getAuthUrl(appid)
}


const getAuthUrl = (appId) => {
	  // #ifdef H5
	  // #endif
	  //cookie.set('redirect', window.location.href)
	  const url = `${location.origin}/h5/#/pages/index/index`
	  //cookie.set('index_url',url)
	  let redirect_uri = encodeURIComponent(url)
	
	  const state = 'STATE'
	  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
 }
 

// 提交
const submit = () => {
	if (testMobible(mobile.value) == false) {
		uToast.value.show({
			message: '手机号码格式不对',
			type: 'error'
		});
		return
	}
	
	if(!isChecked.value){
		uToast.value.show({
			message: '请勾选下面协议',
			type: 'error'
		});
		return
	}
	
	login()

}

// 登录
const login = async () => {
	let from = 'routine'
	// #ifdef H5
	from = 'h5'
	if(util.isWeixin()){
		from = 'wechat'
	}
	
	// #endif
	let data = await userLogin({
		mobile: mobile.value,
		code: captcha.value,
		from: from,
		openid: openid.value
	})
	if (data) {
		uni.setStorage({
			key: 'userinfo',
			data: data.userInfo
		});
		uni.setStorage({
			key: 'accessToken',
			data: data.accessToken
		});
		main.SET_MEMBER(data.userInfo);
		main.SET_TOKEN(data.accessToken);
		uToast.value.show({
			message: '登录成功',
			type: 'success'
		});

		//let newParams = cookie.get('params')
		setTimeout(function() {
			handleLoginSuccess();
		}, 2000);
	}
}

const serv = (id,name) => {
	uni.navigateTo({
			url: '/pages/components/pages/mine/content?id=' + id + '&name=' + name
	})
}

const onChange = () => {
	isChecked.value = !isChecked.value
}

</script>

<style lang="scss" scoped>
.wrap {
	background-color: #ffffff;
	font-size: 28rpx;
	position: relative;
	height: 100%;
	.content {
		width: 600rpx;
		margin: 0 auto;

		.title {
			text-align: left;
			font-size: 60rpx;
			font-weight: 500;
			margin-bottom: 100rpx;
		}
		input {
			text-align: left;
			margin-bottom: 10rpx;
			padding-bottom: 6rpx;
		}
		.tips {
			color: $uv-info;
			margin-bottom: 60rpx;
			margin-top: 8rpx;
		}
		.getCaptcha {
			background-color: #1971F2;
			color: #ffffff;
			border: none;
			font-size: 20rpx;
			padding: 5rpx 0;
			
			&::after {
				border: none;
			}
		}
		.login {
			background-color: #1971F2;
			color: #ffffff;
			border-radius: 50rem !important;
			font-size: 26rpx;
			padding: 8rpx 0;
			margin-top: 40rpx;
			&::after {
				border: none;
			}
		}
		.alternative {
			color: $uv-tips-color;
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
		}
	}
	.buttom {
		//position: absolute;
		bottom: 0;
		//display: flex;
		//flex-direction: column;
		//align-items: center;
		//justify-content: center;
		.loginType {
			padding: 30rpx 80rpx;
			//justify-content:space-between;
			
			.login-btn {
				background-color: #1aad19!important;
				width: 100%;
				font-size: 26rpx;
				border-radius: 50rem !important;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 8rpx 0;
				text-align: center;
				image {
					width: 36rpx;
					height: 30rpx;
					margin-right: 10rpx;
					vertical-align: middle;
				}
			}
		}
		
		.hint {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20rpx 40rpx;
			font-size: 20rpx;
			color: $uv-tips-color;
			
			.link {
				color: $uv-warning;
			}
		}
	}
}
</style>
