# 微信小程序Scene参数处理方案

## 问题描述

微信小程序通过二维码扫码进入时，参数通过`scene`字段传递，格式为双重URL编码的字符串。原有的活动详情页面只处理普通URL参数，无法正确解析scene参数。

## 解决方案

### 1. 创建专用工具函数

创建了 `utils/scene-parser.js` 工具文件，包含以下功能：

- `parseSceneParams(sceneStr)`: 解析scene参数字符串
- `handleSceneParams(options)`: 处理onLoad中的参数（包括URL参数和scene参数）
- `handleHotStartSceneParams()`: 处理小程序热启动时的scene参数

### 2. 修改活动详情页面

修改了 `pages/components/pages/activity/detail.vue`：

- 在`onLoad`方法中使用`handleSceneParams`统一处理参数
- 在`onShow`方法中使用`handleHotStartSceneParams`处理热启动场景
- 简化了代码逻辑，提高了可维护性

### 3. 支持的参数格式

工具函数支持以下格式的scene参数：

```javascript
// 标准格式
"activityId=2&inviter=24"

// URL编码格式
"activityId%3D2%26inviter%3D24"

// 双重编码格式（微信小程序码常用）
"activityId%253D2%2526inviter%253D24"
```

## 使用方法

### 在页面中使用

```javascript
import { handleSceneParams, handleHotStartSceneParams } from '@/utils/scene-parser'

export default {
  onLoad(options) {
    // 统一处理URL参数和scene参数
    const params = handleSceneParams(options)
    
    this.activityId = params.activityId
    this.inviterUserId = params.inviterUserId
    
    console.log('参数来源:', params.source) // 'url', 'scene', 'launch'
  },
  
  onShow() {
    // 处理热启动场景
    if (!this.activityId) {
      const params = handleHotStartSceneParams()
      if (params.activityId) {
        this.activityId = params.activityId
        this.inviterUserId = params.inviterUserId
      }
    }
  }
}
```

### 测试功能

创建了测试页面 `pages/test/scene-test.vue` 用于验证scene参数解析功能：

- 支持手动输入测试参数
- 提供预设测试用例
- 显示解析结果
- 输出详细的控制台日志

## 技术细节

### 参数解析流程

1. **普通URL参数**: 直接从`options.id`和`options.inviter`获取
2. **Scene参数**: 
   - 从`options.scene`获取并解码
   - 使用`urlDecode`工具函数解析
   - 备用手动解析方法
3. **启动参数**: 通过`wx.getLaunchOptionsSync()`获取场景值1047的参数

### 场景值说明

- `1047`: 扫描小程序码
- `1008`: 群聊会话中的小程序消息卡片
- `1036`: App分享消息卡片
- `1037`: 小程序打开小程序
- `1038`: 从另一个小程序返回

### 错误处理

- 使用try-catch包装所有解析逻辑
- 提供备用解析方法
- 详细的控制台日志输出
- 参数验证和默认值处理

## 测试验证

### 测试用例

1. **标准格式**: `activityId=2&inviter=24`
2. **URL编码**: `activityId%3D2%26inviter%3D24`
3. **双重编码**: `activityId%253D2%2526inviter%253D24`
4. **只有活动ID**: `activityId=5`
5. **复杂参数**: `activityId=10&inviter=100&extra=test`

### 验证方法

1. 使用测试页面手动验证
2. 查看控制台日志确认解析过程
3. 验证不同场景下的参数获取

## 注意事项

1. **编码问题**: 微信小程序码的scene参数是双重URL编码的
2. **生命周期**: 需要在`onLoad`和`onShow`中都处理scene参数
3. **兼容性**: 保持对原有URL参数方式的兼容
4. **错误处理**: 确保解析失败时不影响页面正常功能

## 相关文件

- `utils/scene-parser.js`: 核心工具函数
- `pages/components/pages/activity/detail.vue`: 活动详情页面
- `pages/test/scene-test.vue`: 测试页面
- `utils/util.js`: 原有的urlDecode工具函数
